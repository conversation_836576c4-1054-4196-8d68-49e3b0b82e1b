-- Demo Data Generation Script for Supabase Dashboard SQL Editor
-- This script creates 5 realistic clients with 2-5 projects each, then populates them with demo data
-- Execute this script in the Supabase Dashboard SQL Editor

DO $$
DECLARE
    -- Use existing organization and user from seeds
    v_org_id UUID := '6d8f7927-c31c-46b0-9dfd-fbc2b530088a'; -- Aurora organization
    v_user_id UUID := 'e7909c19-9a50-41f1-b1b7-26553bedd0a7'; -- Test user

    -- Client variables
    v_client_id UUID;
    v_result JSONB;
    v_generated_project_id UUID;

    -- Arrays of realistic client and project data
    client_names TEXT[] := ARRAY[
        'Meridian Construction Group',
        'Apex Engineering Solutions',
        'Cornerstone Development Partners',
        'Pinnacle Infrastructure Ltd',
        'Horizon Building Contractors'
    ];

    client_descriptions TEXT[] := ARRAY[
        'Leading commercial construction firm specializing in high-rise developments and mixed-use projects',
        'Full-service engineering consultancy providing structural, civil, and MEP design services',
        'Residential and commercial property development company with 25+ years of experience',
        'Infrastructure specialists focusing on transportation, utilities, and public works projects',
        'General contracting company serving the healthcare, education, and hospitality sectors'
    ];

    -- Project data organized by client type
    commercial_projects TEXT[] := ARRAY[
        'Downtown Financial Center',
        'Riverside Office Complex',
        'Metro Shopping District',
        'Corporate Campus Phase II',
        'Innovation Hub Tower'
    ];

    engineering_projects TEXT[] := ARRAY[
        'City Bridge Rehabilitation',
        'Water Treatment Facility Upgrade',
        'Transit Station Modernization',
        'Industrial Park Infrastructure'
    ];

    residential_projects TEXT[] := ARRAY[
        'Lakeside Residential Community',
        'Urban Lofts Development',
        'Affordable Housing Initiative',
        'Senior Living Complex',
        'Townhome Village Phase I'
    ];

    infrastructure_projects TEXT[] := ARRAY[
        'Highway Interchange Reconstruction',
        'Municipal Wastewater Expansion',
        'Regional Airport Terminal',
        'Smart City Fiber Network'
    ];

    healthcare_projects TEXT[] := ARRAY[
        'Regional Medical Center Expansion',
        'University Hospital Renovation',
        'Outpatient Surgery Center',
        'Emergency Department Upgrade',
        'Pediatric Wing Addition'
    ];

    project_descriptions TEXT[] := ARRAY[
        'Modern facility designed to meet growing community needs with sustainable construction practices',
        'State-of-the-art development incorporating latest building technologies and energy efficiency standards',
        'Comprehensive renovation and expansion project enhancing operational capacity and patient experience',
        'New construction project featuring innovative design and advanced building systems',
        'Multi-phase development project supporting long-term growth and community development goals'
    ];

    i INTEGER;
    j INTEGER;
    project_count INTEGER;
    selected_projects TEXT[];
    total_projects_created INTEGER := 0;

BEGIN
    RAISE NOTICE 'Starting demo data generation...';

    -- Set auth context for the demo data generation function
    PERFORM set_config('request.jwt.claims', '{"sub":"' || v_user_id || '"}', true);

    -- Create 5 clients
    FOR i IN 1..5 LOOP
        -- Insert client
        INSERT INTO public.client (
            name,
            description,
            created_by_user_id,
            org_id
        ) VALUES (
            client_names[i],
            client_descriptions[i],
            v_user_id,
            v_org_id
        ) RETURNING client_id INTO v_client_id;

        RAISE NOTICE 'Created client: % (ID: %)', client_names[i], v_client_id;

        -- Determine project count (2-5 projects per client)
        project_count := 2 + FLOOR(RANDOM() * 4)::INTEGER;

        -- Select appropriate project array based on client type
        CASE i
            WHEN 1 THEN selected_projects := commercial_projects;
            WHEN 2 THEN selected_projects := engineering_projects;
            WHEN 3 THEN selected_projects := residential_projects;
            WHEN 4 THEN selected_projects := infrastructure_projects;
            WHEN 5 THEN selected_projects := healthcare_projects;
        END CASE;

        -- Create projects for this client by calling generate_demo_project_data multiple times
        FOR j IN 1..project_count LOOP
            -- Store the original Unity client name temporarily
            UPDATE public.client
            SET name = 'Unity_temp_' || client_id::TEXT
            WHERE name = 'Unity' AND org_id = v_org_id;

            -- Temporarily rename our new client to Unity
            UPDATE public.client
            SET name = 'Unity'
            WHERE client_id = v_client_id;

            -- Call the demo data generation function (creates project under Unity client)
            SELECT public.generate_demo_project_data() INTO v_result;

            -- Restore the original client name
            UPDATE public.client
            SET name = client_names[i]
            WHERE client_id = v_client_id;

            -- Restore the original Unity client name if it existed
            UPDATE public.client
            SET name = 'Unity'
            WHERE name LIKE 'Unity_temp_%' AND org_id = v_org_id;

            -- Check if generation was successful
            IF (v_result->>'success')::BOOLEAN THEN
                v_generated_project_id := (v_result->>'project_id')::UUID;
                total_projects_created := total_projects_created + 1;

                RAISE NOTICE 'Generated demo project ID: %', v_generated_project_id;

                -- Update the generated project to belong to our new client and have realistic name
                UPDATE public.project
                SET
                    client_id = v_client_id,
                    name = selected_projects[((j-1) % array_length(selected_projects, 1)) + 1],
                    description = project_descriptions[((j-1) % array_length(project_descriptions, 1)) + 1]
                WHERE project_id = v_generated_project_id;

                RAISE NOTICE 'Updated project to client % with name: %', client_names[i], selected_projects[((j-1) % array_length(selected_projects, 1)) + 1];
            ELSE
                RAISE WARNING 'Failed to generate demo data for project: %', v_result->>'error';
            END IF;
        END LOOP;

        RAISE NOTICE 'Completed % projects for client: %', project_count, client_names[i];
    END LOOP;

    RAISE NOTICE 'Demo data generation completed successfully!';
    RAISE NOTICE 'Created 5 clients with a total of % projects', total_projects_created;

    -- Final cleanup: Ensure any temporary Unity client names are restored
    UPDATE public.client
    SET name = 'Unity'
    WHERE name LIKE 'Unity_temp_%' AND org_id = v_org_id;

END $$;

-- Display summary of created data
SELECT
    'DEMO DATA SUMMARY' AS summary_title;

SELECT
    c.name AS client_name,
    c.description AS client_description,
    COUNT(p.project_id) AS project_count,
    STRING_AGG(p.name, ', ' ORDER BY p.name) AS project_names
FROM public.client c
LEFT JOIN public.project p ON c.client_id = p.client_id
WHERE c.org_id = '6d8f7927-c31c-46b0-9dfd-fbc2b530088a'
  AND c.name != 'Unity' -- Exclude the original Unity client
GROUP BY c.client_id, c.name, c.description
ORDER BY c.name;

-- Show total counts
SELECT
    'TOTALS' AS summary_section,
    COUNT(DISTINCT c.client_id) AS total_clients,
    COUNT(p.project_id) AS total_projects,
    COUNT(ps.project_stage_id) AS total_project_stages,
    COUNT(DISTINCT bli.budget_line_item_id) AS total_budget_line_items,
    COUNT(DISTINCT bs.budget_snapshot_id) AS total_budget_snapshots,
    COUNT(DISTINCT rr.risk_id) AS total_risks
FROM public.client c
LEFT JOIN public.project p ON c.client_id = p.client_id
LEFT JOIN public.project_stage ps ON p.project_id = ps.project_id
LEFT JOIN public.budget_line_item_current bli ON p.project_id = bli.project_id
LEFT JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
LEFT JOIN public.risk_register rr ON p.project_id = rr.project_id
WHERE c.org_id = '6d8f7927-c31c-46b0-9dfd-fbc2b530088a'
  AND c.name != 'Unity';

-- Show sample budget data from current budget line items
SELECT
    'SAMPLE CURRENT BUDGET DATA' AS data_section,
    c.name AS client_name,
    p.name AS project_name,
    COUNT(bli.budget_line_item_id) AS budget_line_items_count,
    ROUND(SUM(bli.quantity * bli.unit_rate * COALESCE(bli.factor, 1))::NUMERIC, 2) AS project_total
FROM public.client c
JOIN public.project p ON c.client_id = p.client_id
JOIN public.budget_line_item_current bli ON p.project_id = bli.project_id
WHERE c.org_id = '6d8f7927-c31c-46b0-9dfd-fbc2b530088a'
  AND c.name != 'Unity'
GROUP BY c.name, p.name
ORDER BY c.name, p.name
LIMIT 10;

-- Show sample budget snapshot data
SELECT
    'SAMPLE BUDGET SNAPSHOT DATA' AS data_section,
    c.name AS client_name,
    p.name AS project_name,
    ps.name AS stage_name,
    COUNT(bsli.budget_snapshot_line_item_id) AS snapshot_items_count,
    ROUND(SUM(bsli.quantity * bsli.unit_rate * COALESCE(bsli.factor, 1))::NUMERIC, 2) AS stage_total
FROM public.client c
JOIN public.project p ON c.client_id = p.client_id
JOIN public.project_stage ps ON p.project_id = ps.project_id
JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
JOIN public.budget_snapshot_line_item bsli ON bs.budget_snapshot_id = bsli.budget_snapshot_id
WHERE c.org_id = '6d8f7927-c31c-46b0-9dfd-fbc2b530088a'
  AND c.name != 'Unity'
GROUP BY c.name, p.name, ps.name, ps.stage_order
ORDER BY c.name, p.name, ps.stage_order
LIMIT 10;
